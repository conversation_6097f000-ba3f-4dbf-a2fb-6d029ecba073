/**
 * CORS Configuration Demonstration
 * 
 * This file demonstrates the improved CORS configuration features:
 * - Security enforcement in production
 * - Origin validation with logging
 * - Error handling with custom error types
 * - Environment-based behavior
 */

import corsConfig, { corsConfigDetails } from '../config/cors'

/**
 * Demonstrates CORS origin validation behavior
 */
function demonstrateCorsValidation(): void {
  console.log('=== CORS Configuration Demo ===\n')
  
  // Show current configuration
  console.log('Current Configuration:')
  console.log(`- Environment: ${process.env.NODE_ENV || 'development'}`)
  console.log(`- Is Production: ${corsConfigDetails.isProduction}`)
  console.log(`- Allowed Origins: ${corsConfigDetails.allowedOrigins ? corsConfigDetails.allowedOrigins.join(', ') : 'None configured'}`)
  console.log(`- Credentials Allowed: ${corsConfig.credentials}`)
  console.log(`- Max Age: ${corsConfig.maxAge} seconds`)
  console.log(`- Methods: ${corsConfig.methods?.join(', ')}`)
  console.log(`- Allowed Headers: ${corsConfig.allowedHeaders?.join(', ')}`)
  console.log()

  // Test different origin scenarios
  const testOrigins = [
    undefined, // Same-origin request
    'http://localhost:3000',
    'https://example.com',
    'https://malicious.com'
  ]

  console.log('Testing Origin Validation:')
  console.log('=' .repeat(50))

  testOrigins.forEach((origin, index) => {
    console.log(`\nTest ${index + 1}: ${origin || 'same-origin'}`)
    
    if (typeof corsConfig.origin === 'function') {
      corsConfig.origin(origin, (error, allowed) => {
        if (error) {
          console.log(`❌ BLOCKED: ${error.message}`)
        } else if (allowed) {
          console.log(`✅ ALLOWED`)
        } else {
          console.log(`❌ BLOCKED: Unknown reason`)
        }
      })
    }
  })

  console.log('\n' + '='.repeat(50))
  console.log('Demo completed. Check console for security logs.')
}

/**
 * Shows configuration recommendations based on environment
 */
function showConfigurationRecommendations(): void {
  console.log('\n=== Configuration Recommendations ===\n')
  
  if (corsConfigDetails.isProduction) {
    console.log('🔒 PRODUCTION ENVIRONMENT DETECTED')
    console.log('Recommendations:')
    console.log('- ✅ CORS whitelist enforcement is active')
    console.log('- ✅ Security logging is enabled')
    console.log('- ⚠️  Ensure WHITELIST_URLS contains only trusted domains')
    console.log('- ⚠️  Monitor CORS security logs for blocked requests')
  } else {
    console.log('🔧 DEVELOPMENT ENVIRONMENT DETECTED')
    console.log('Recommendations:')
    console.log('- ⚠️  CORS is more permissive in development')
    console.log('- ✅ Security logging shows detailed information')
    console.log('- 💡 Test with production-like WHITELIST_URLS before deploying')
    console.log('- 💡 Set NODE_ENV=production to test production behavior')
  }

  console.log('\nSecurity Features:')
  console.log('- ✅ URL format validation')
  console.log('- ✅ Custom error types with context')
  console.log('- ✅ Comprehensive CORS options')
  console.log('- ✅ Environment-based security enforcement')
  console.log('- ✅ Security event logging')
}

// Run the demonstration if this file is executed directly
if (require.main === module) {
  try {
    demonstrateCorsValidation()
    showConfigurationRecommendations()
  } catch (error) {
    console.error('Demo failed:', error)
    process.exit(1)
  }
}

export { demonstrateCorsValidation, showConfigurationRecommendations }
