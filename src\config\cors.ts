import { type CorsOptions } from 'cors'
import { ConfigurationError, InvalidConfigurationError } from '../common/errors'

/**
 * CORS Configuration Module
 *
 * Provides secure Cross-Origin Resource Sharing configuration with:
 * - Environment-based origin whitelisting
 * - Production security enforcement
 * - Comprehensive CORS options
 * - Security logging for blocked requests
 * - Input validation for configuration
 *
 * <AUTHOR> Backend Team
 * @version 1.0.0
 */

// -----------------------------------------------
// Constants and Configuration
// -----------------------------------------------

/** Default CORS configuration values */
const CORS_DEFAULTS = {
  METHODS: [
    'GET',
    'HEAD',
    'PUT',
    'PATCH',
    'POST',
    'DELETE',
    'OPTIONS'
  ] as string[],
  ALLOWED_HEADERS: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'Cache-Control',
    'Pragma'
  ] as string[],
  EXPOSED_HEADERS: ['X-Total-Count', 'X-Page-Count'] as string[],
  MAX_AGE: 86400, // 24 hours in seconds
  OPTIONS_SUCCESS_STATUS: 200
} as const

/** URL validation regex pattern */
const URL_PATTERN = /^https?:\/\/(?:[-\w.])+(?::[0-9]+)?(?:\/.*)?$/

// -----------------------------------------------
// Utility Functions
// -----------------------------------------------

/**
 * Validates a URL string format
 * @param url - The URL to validate
 * @returns True if URL is valid, false otherwise
 */
const isValidUrl = (url: string): boolean => {
  return URL_PATTERN.test(url.trim())
}

/**
 * Parses and validates the WHITELIST_URLS environment variable
 * @returns Array of validated origin URLs or undefined if not configured
 * @throws InvalidConfigurationError if URLs are malformed
 */
const parseOrigins = (): string[] | undefined => {
  const whitelistUrls = process.env.WHITELIST_URLS?.trim()

  if (!whitelistUrls) {
    return undefined
  }

  const origins = whitelistUrls
    .split(',')
    .map((url) => url.trim())
    .filter((url) => url.length > 0)

  // Validate each URL format
  const invalidUrls = origins.filter((url) => !isValidUrl(url))
  if (invalidUrls.length > 0) {
    throw new InvalidConfigurationError(
      `CORS origins contain invalid URLs: ${invalidUrls.join(', ')}`,
      {
        details: { invalidUrls, allOrigins: origins }
      }
    )
  }

  return origins.length > 0 ? origins : undefined
}

/**
 * Logs security events for monitoring purposes
 * @param event - The security event type
 * @param details - Additional event details
 */
const logSecurityEvent = (
  event: 'CORS_BLOCKED' | 'CORS_ALLOWED' | 'CORS_CONFIG_ERROR',
  details: Record<string, unknown>
): void => {
  const timestamp = new Date().toISOString()
  const logEntry = {
    timestamp,
    event,
    ...details
  }

  if (process.env.NODE_ENV === 'development') {
    console.warn(`[CORS Security] ${event}:`, logEntry)
  } else {
    // In production, you might want to use a proper logging service
    console.warn(`[CORS Security] ${timestamp} - ${event}`)
  }
}

// -----------------------------------------------
// CORS Configuration
// -----------------------------------------------

let allowedOrigins: string[] | undefined

try {
  allowedOrigins = parseOrigins()
} catch (error) {
  // Log configuration error and re-throw
  logSecurityEvent('CORS_CONFIG_ERROR', {
    error:
      error instanceof Error ? error.message : 'Unknown configuration error'
  })
  throw error
}

/**
 * CORS origin validation function
 * Implements secure origin checking with proper error handling and logging
 */
const corsOriginHandler = (
  origin: string | undefined,
  callback: (err: Error | null, allow?: boolean) => void
): void => {
  // Handle same-origin requests (no origin header)
  if (origin === undefined) {
    // Allow same-origin requests (e.g., from Postman, server-to-server)
    logSecurityEvent('CORS_ALLOWED', {
      origin: 'same-origin',
      reason: 'No origin header (same-origin request)'
    })
    callback(null, true)
    return
  }

  // Production security: Reject all requests if no whitelist is configured
  if (!allowedOrigins && process.env.NODE_ENV === 'production') {
    const error = new ConfigurationError(
      'CORS whitelist not configured for production environment',
      500,
      {
        details: {
          origin,
          environment: process.env.NODE_ENV,
          suggestion: 'Set WHITELIST_URLS environment variable'
        }
      }
    )

    logSecurityEvent('CORS_BLOCKED', {
      origin,
      reason: 'No whitelist configured in production',
      blocked: true
    })

    callback(error)
    return
  }

  // Development mode: Allow all origins if no whitelist is configured
  if (!allowedOrigins) {
    logSecurityEvent('CORS_ALLOWED', {
      origin,
      reason: 'Development mode - no whitelist configured',
      warning: 'This should not happen in production'
    })
    callback(null, true)
    return
  }

  // Check if origin is in the whitelist
  const isAllowed = allowedOrigins.includes(origin)

  if (isAllowed) {
    logSecurityEvent('CORS_ALLOWED', {
      origin,
      reason: 'Origin found in whitelist'
    })
    callback(null, true)
  } else {
    const error = new ConfigurationError(
      `CORS request blocked: Origin '${origin}' not in whitelist`,
      403,
      {
        details: {
          origin,
          allowedOrigins,
          suggestion: 'Add origin to WHITELIST_URLS environment variable'
        }
      }
    )

    logSecurityEvent('CORS_BLOCKED', {
      origin,
      reason: 'Origin not in whitelist',
      allowedOrigins,
      blocked: true
    })

    callback(error)
  }
}

/**
 * Complete CORS configuration object
 * Implements security best practices for Cross-Origin Resource Sharing
 */
const corsConfig: CorsOptions = {
  // Origin validation with security logging
  origin: corsOriginHandler,

  // Allow credentials (cookies, authorization headers)
  credentials: true,

  // Specify allowed HTTP methods
  methods: CORS_DEFAULTS.METHODS,

  // Specify allowed request headers
  allowedHeaders: CORS_DEFAULTS.ALLOWED_HEADERS,

  // Specify headers that clients can access
  exposedHeaders: CORS_DEFAULTS.EXPOSED_HEADERS,

  // Cache preflight response for 24 hours
  maxAge: CORS_DEFAULTS.MAX_AGE,

  // Success status for OPTIONS requests
  optionsSuccessStatus: CORS_DEFAULTS.OPTIONS_SUCCESS_STATUS,

  // Continue to next handler after successful preflight
  preflightContinue: false
}

// -----------------------------------------------
// Exports
// -----------------------------------------------

export default corsConfig

/**
 * Export configuration details for testing and debugging
 */
export const corsConfigDetails = {
  allowedOrigins,
  defaults: CORS_DEFAULTS,
  isProduction: process.env.NODE_ENV === 'production'
}
