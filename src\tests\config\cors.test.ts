import { type CorsOptions } from 'cors'
import { ConfigurationError, InvalidConfigurationError } from '../../common/errors'

/**
 * CORS Configuration Tests
 * 
 * Tests the CORS configuration module for:
 * - Security enforcement in production
 * - Proper origin validation
 * - Error handling and logging
 * - Configuration validation
 * - Environment-based behavior
 */

describe('CORS Configuration', () => {
  // Store original environment variables
  const originalEnv = process.env

  beforeEach(() => {
    // Reset environment for each test
    jest.resetModules()
    process.env = { ...originalEnv }
    
    // Mock console methods to capture logs
    jest.spyOn(console, 'warn').mockImplementation(() => {})
    jest.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv
    jest.restoreAllMocks()
  })

  describe('Configuration Parsing', () => {
    it('should parse valid WHITELIST_URLS correctly', async () => {
      process.env.WHITELIST_URLS = 'http://localhost:3000,https://example.com'
      
      const { corsConfigDetails } = await import('../../config/cors')
      
      expect(corsConfigDetails.allowedOrigins).toEqual([
        'http://localhost:3000',
        'https://example.com'
      ])
    })

    it('should handle empty WHITELIST_URLS', async () => {
      delete process.env.WHITELIST_URLS
      
      const { corsConfigDetails } = await import('../../config/cors')
      
      expect(corsConfigDetails.allowedOrigins).toBeUndefined()
    })

    it('should trim whitespace from URLs', async () => {
      process.env.WHITELIST_URLS = ' http://localhost:3000 , https://example.com '
      
      const { corsConfigDetails } = await import('../../config/cors')
      
      expect(corsConfigDetails.allowedOrigins).toEqual([
        'http://localhost:3000',
        'https://example.com'
      ])
    })

    it('should throw InvalidConfigurationError for malformed URLs', async () => {
      process.env.WHITELIST_URLS = 'invalid-url,http://valid.com'
      
      await expect(async () => {
        await import('../../config/cors')
      }).rejects.toThrow(InvalidConfigurationError)
    })

    it('should filter out empty URL entries', async () => {
      process.env.WHITELIST_URLS = 'http://localhost:3000,,https://example.com,'
      
      const { corsConfigDetails } = await import('../../config/cors')
      
      expect(corsConfigDetails.allowedOrigins).toEqual([
        'http://localhost:3000',
        'https://example.com'
      ])
    })
  })

  describe('Origin Validation', () => {
    it('should allow same-origin requests (undefined origin)', async () => {
      process.env.WHITELIST_URLS = 'http://localhost:3000'
      process.env.NODE_ENV = 'production'
      
      const corsConfig = (await import('../../config/cors')).default
      const mockCallback = jest.fn()
      
      // Simulate CORS origin check
      if (typeof corsConfig.origin === 'function') {
        corsConfig.origin(undefined, mockCallback)
      }
      
      expect(mockCallback).toHaveBeenCalledWith(null, true)
    })

    it('should allow whitelisted origins', async () => {
      process.env.WHITELIST_URLS = 'http://localhost:3000,https://example.com'
      process.env.NODE_ENV = 'production'
      
      const corsConfig = (await import('../../config/cors')).default
      const mockCallback = jest.fn()
      
      if (typeof corsConfig.origin === 'function') {
        corsConfig.origin('http://localhost:3000', mockCallback)
      }
      
      expect(mockCallback).toHaveBeenCalledWith(null, true)
    })

    it('should block non-whitelisted origins', async () => {
      process.env.WHITELIST_URLS = 'http://localhost:3000'
      process.env.NODE_ENV = 'production'
      
      const corsConfig = (await import('../../config/cors')).default
      const mockCallback = jest.fn()
      
      if (typeof corsConfig.origin === 'function') {
        corsConfig.origin('https://malicious.com', mockCallback)
      }
      
      expect(mockCallback).toHaveBeenCalledWith(
        expect.any(ConfigurationError),
        undefined
      )
    })

    it('should block all origins in production when no whitelist is configured', async () => {
      delete process.env.WHITELIST_URLS
      process.env.NODE_ENV = 'production'
      
      const corsConfig = (await import('../../config/cors')).default
      const mockCallback = jest.fn()
      
      if (typeof corsConfig.origin === 'function') {
        corsConfig.origin('https://example.com', mockCallback)
      }
      
      expect(mockCallback).toHaveBeenCalledWith(
        expect.any(ConfigurationError),
        undefined
      )
    })

    it('should allow all origins in development when no whitelist is configured', async () => {
      delete process.env.WHITELIST_URLS
      process.env.NODE_ENV = 'development'
      
      const corsConfig = (await import('../../config/cors')).default
      const mockCallback = jest.fn()
      
      if (typeof corsConfig.origin === 'function') {
        corsConfig.origin('https://example.com', mockCallback)
      }
      
      expect(mockCallback).toHaveBeenCalledWith(null, true)
    })
  })

  describe('Security Logging', () => {
    it('should log blocked requests', async () => {
      process.env.WHITELIST_URLS = 'http://localhost:3000'
      process.env.NODE_ENV = 'development'
      
      const corsConfig = (await import('../../config/cors')).default
      const mockCallback = jest.fn()
      
      if (typeof corsConfig.origin === 'function') {
        corsConfig.origin('https://malicious.com', mockCallback)
      }
      
      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining('[CORS Security] CORS_BLOCKED:'),
        expect.objectContaining({
          origin: 'https://malicious.com',
          blocked: true
        })
      )
    })

    it('should log allowed requests', async () => {
      process.env.WHITELIST_URLS = 'http://localhost:3000'
      process.env.NODE_ENV = 'development'
      
      const corsConfig = (await import('../../config/cors')).default
      const mockCallback = jest.fn()
      
      if (typeof corsConfig.origin === 'function') {
        corsConfig.origin('http://localhost:3000', mockCallback)
      }
      
      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining('[CORS Security] CORS_ALLOWED:'),
        expect.objectContaining({
          origin: 'http://localhost:3000'
        })
      )
    })
  })

  describe('CORS Configuration Object', () => {
    it('should have all required CORS options', async () => {
      process.env.WHITELIST_URLS = 'http://localhost:3000'
      
      const corsConfig = (await import('../../config/cors')).default
      
      expect(corsConfig).toHaveProperty('origin')
      expect(corsConfig).toHaveProperty('credentials', true)
      expect(corsConfig).toHaveProperty('methods')
      expect(corsConfig).toHaveProperty('allowedHeaders')
      expect(corsConfig).toHaveProperty('exposedHeaders')
      expect(corsConfig).toHaveProperty('maxAge', 86400)
      expect(corsConfig).toHaveProperty('optionsSuccessStatus', 200)
      expect(corsConfig).toHaveProperty('preflightContinue', false)
    })

    it('should include security headers in allowed headers', async () => {
      const corsConfig = (await import('../../config/cors')).default
      
      expect(corsConfig.allowedHeaders).toContain('Authorization')
      expect(corsConfig.allowedHeaders).toContain('Content-Type')
      expect(corsConfig.allowedHeaders).toContain('Origin')
    })

    it('should include all necessary HTTP methods', async () => {
      const corsConfig = (await import('../../config/cors')).default
      
      expect(corsConfig.methods).toContain('GET')
      expect(corsConfig.methods).toContain('POST')
      expect(corsConfig.methods).toContain('PUT')
      expect(corsConfig.methods).toContain('DELETE')
      expect(corsConfig.methods).toContain('PATCH')
      expect(corsConfig.methods).toContain('OPTIONS')
    })
  })

  describe('Environment Detection', () => {
    it('should correctly detect production environment', async () => {
      process.env.NODE_ENV = 'production'
      
      const { corsConfigDetails } = await import('../../config/cors')
      
      expect(corsConfigDetails.isProduction).toBe(true)
    })

    it('should correctly detect development environment', async () => {
      process.env.NODE_ENV = 'development'
      
      const { corsConfigDetails } = await import('../../config/cors')
      
      expect(corsConfigDetails.isProduction).toBe(false)
    })
  })
})
